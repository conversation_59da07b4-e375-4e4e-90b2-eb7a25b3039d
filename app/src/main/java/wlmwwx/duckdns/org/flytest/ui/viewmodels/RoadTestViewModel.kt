package wlmwwx.duckdns.org.flytest.ui.viewmodels

import android.app.Application
import android.content.Context
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.viewModelScope
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch
import wlmwwx.duckdns.org.flytest.logic.model.RadioInfoModel
import wlmwwx.duckdns.org.flytest.logic.repository.RadioInfoRepository
import wlmwwx.duckdns.org.flytest.logic.model.LocationInfoModel
import wlmwwx.duckdns.org.flytest.logic.repository.LocationInfoRepository
import wlmwwx.duckdns.org.flytest.ui.screens.LocationInfoPanel


class RoadTestViewModel(application: Application) : AndroidViewModel(application) {

    private val context: Context = application.applicationContext
    private val radioInfoRepository = RadioInfoRepository(context)
    private val locationInfoRepository = LocationInfoRepository(context)

    private val _lteInfo = MutableStateFlow(RadioInfoModel.LteInfo())
    val lteInfo: StateFlow<RadioInfoModel.LteInfo> = _lteInfo

    private val _gpsInfo = MutableStateFlow(LocationInfoModel.LocationInfo())
    val gpsInfo: StateFlow<LocationInfoModel.LocationInfo> = _gpsInfo



    private val _nrInfo = MutableStateFlow(RadioInfoModel.NrInfo())
    val nrInfo: StateFlow<RadioInfoModel.NrInfo> = _nrInfo

    private val _wcdmaInfo = MutableStateFlow(RadioInfoModel.WcdmaInfo())
    val wcdmaInfo: StateFlow<RadioInfoModel.WcdmaInfo> = _wcdmaInfo

    private val _gsmInfo = MutableStateFlow(RadioInfoModel.GsmInfo())
    val gsmInfo: StateFlow<RadioInfoModel.GsmInfo> = _gsmInfo

    private val _cellType = MutableStateFlow("NONE")
    val cellType: StateFlow<String> = _cellType

    private val _locationInfo = MutableStateFlow(LocationInfoModel.LocationInfo())
    val locationInfo: StateFlow<LocationInfoModel.LocationInfo> = _locationInfo

    init {
        // Set up radio info listener
        radioInfoRepository.setRadioInfoListener(object : RadioInfoModel.RadioInfoListener {
            override fun onLteInfoUpdated(info: RadioInfoModel.LteInfo) {
                _lteInfo.value = info
            }
            override fun onNrInfoUpdated(info: RadioInfoModel.NrInfo) {
                _nrInfo.value = info
            }
            override fun onWcdmaInfoUpdated(info: RadioInfoModel.WcdmaInfo) {
                _wcdmaInfo.value = info
            }
            override fun onGsmInfoUpdated(info: RadioInfoModel.GsmInfo) {
                _gsmInfo.value = info
            }
            override fun onCellTypeUpdated(type: String) {
                _cellType.value = type
            }
        })

        // Set up location info listener
        locationInfoRepository.setLocationInfoListener(object : LocationInfoModel.LocationInfoListener {
            override fun onLocationUpdated(info: LocationInfoModel.LocationInfo) {
                _locationInfo.value = info
            }
        })

        // Start updates
        radioInfoRepository.startRadioInfoUpdates()
        locationInfoRepository.startLocationUpdates()

        // Initial values
        _locationInfo.value = locationInfoRepository.getLocationInfo()
    }

    // This can be removed if you want to rely solely on push updates
    fun refreshLocationInfo() {
        _locationInfo.value = locationInfoRepository.getLocationInfo()
    }

    override fun onCleared() {
        super.onCleared()
        radioInfoRepository.stopRadioInfoUpdates()
        radioInfoRepository.setRadioInfoListener(null)
        locationInfoRepository.stopLocationUpdates()
        locationInfoRepository.setLocationInfoListener(null)
    }
} 