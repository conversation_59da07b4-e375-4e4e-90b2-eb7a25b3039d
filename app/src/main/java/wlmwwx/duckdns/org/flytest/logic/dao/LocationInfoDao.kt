package wlmwwx.duckdns.org.flytest.logic.dao

import wlmwwx.duckdns.org.flytest.logic.model.LocationInfoModel

class LocationInfoDao {
    // In-memory cache for LocationInfo data
    var locationInfo: LocationInfoModel.LocationInfo? = null

    fun saveLocationInfo(info: LocationInfoModel.LocationInfo) {
        locationInfo = info
    }
    fun getLocationInfo(): LocationInfoModel.LocationInfo? = locationInfo
} 