package wlmwwx.duckdns.org.flytest.logic.repository

import android.content.Context
import wlmwwx.duckdns.org.flytest.logic.model.RadioInfoModel
import wlmwwx.duckdns.org.flytest.logic.dao.RadioInfoDao

class RadioInfoRepository(context: Context) {
    private val model = RadioInfoModel(context)
    private val dao = RadioInfoDao()

    fun startRadioInfoUpdates() {
        model.registerCallbacks()
    }

    fun stopRadioInfoUpdates() {
        model.unregisterCallbacks()
    }

    fun setRadioInfoListener(listener: RadioInfoModel.RadioInfoListener?) {
        model.listener = listener
    }

    // Example: get latest LTE info
    fun getLteInfo(): RadioInfoModel.LteInfo {
        val info = model.lte
        dao.saveLteInfo(info)
        return info
    }

    fun getNrInfo(): RadioInfoModel.NrInfo {
        val info = model.nr
        // Optionally add dao.saveNrInfo(info) if you add such a method
        return info
    }

    fun getWcdmaInfo(): RadioInfoModel.WcdmaInfo {
        val info = model.wcdma
        // Optionally add dao.saveWcdmaInfo(info) if you add such a method
        return info
    }

    fun getGsmInfo(): RadioInfoModel.GsmInfo {
        val info = model.gsm
        // Optionally add dao.saveGsmInfo(info) if you add such a method
        return info
    }

    fun getCellType(): String {
        return model.cellType
    }

    // ... (other methods for different network types)
} 