package wlmwwx.duckdns.org.flytest.ui.screens

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.PlayArrow
import androidx.compose.material.icons.filled.Refresh
import androidx.compose.material.icons.filled.Settings
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.platform.LocalContext
import androidx.lifecycle.viewmodel.compose.viewModel
import wlmwwx.duckdns.org.flytest.ui.viewmodels.RoadTestViewModel
// import wlmwwx.duckdns.org.flytest.infomonitor.GpsInfo
// import wlmwwx.duckdns.org.flytest.infomonitor.NetworkInfo
import java.util.Locale
import wlmwwx.duckdns.org.flytest.utils.FormatUtils.format
import wlmwwx.duckdns.org.flytest.logic.model.RadioInfoModel
import wlmwwx.duckdns.org.flytest.logic.model.LocationInfoModel



@Composable
fun RoadTestScreen(
    paddingValues: PaddingValues,
    viewModel: RoadTestViewModel = viewModel()
) {
    Scaffold(
        bottomBar = { BottomControls() }
    ) { innerPadding ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(innerPadding)
                .padding(horizontal = 8.dp)
                .padding(top = 56.dp)
        ) {
            val locationInfo = viewModel.locationInfo.collectAsState().value
            LocationInfoPanel(locationInfo = locationInfo)
            Spacer(modifier = Modifier.height(16.dp))
            val cellType = viewModel.cellType.collectAsState().value
            when (cellType) {
                "LTE" -> LteInfoPanel(
                    lteInfo = viewModel.lteInfo.collectAsState().value
                )
                "NR" -> NrInfoPanel(
                    nrInfo = viewModel.nrInfo.collectAsState().value
                )
                "WCDMA" -> WcdmaInfoPanel(
                    wcdmaInfo = viewModel.wcdmaInfo.collectAsState().value
                )
                "GSM" -> GsmInfoPanel(
                    gsmInfo = viewModel.gsmInfo.collectAsState().value
                )
                else -> LteInfoPanel(
                    lteInfo = viewModel.lteInfo.collectAsState().value
                )
            }

        }
    }
}

@Composable
fun LteInfoPanel(
    lteInfo: wlmwwx.duckdns.org.flytest.logic.model.RadioInfoModel.LteInfo,
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(modifier = Modifier.padding(12.dp)) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text("基站信息", style = MaterialTheme.typography.titleMedium, fontWeight = FontWeight.Bold)
                Row(verticalAlignment = Alignment.CenterVertically) {
                    Text("当前网络: LTE", style = MaterialTheme.typography.bodyMedium, color = Color.Gray)
                    Spacer(modifier = Modifier.width(8.dp))
                    OutlinedButton(
                        onClick = { /* TODO: Handle neighbors info click */ },
                        contentPadding = PaddingValues(horizontal = 8.dp, vertical = 4.dp),
                        modifier = Modifier.height(30.dp)
                    ) {
                        Text("邻区信息", fontSize = 12.sp)
                    }
                }
            }
            Spacer(modifier = Modifier.height(8.dp))
            Divider()
            Spacer(modifier = Modifier.height(12.dp))
            Row(Modifier.fillMaxWidth(), horizontalArrangement = Arrangement.SpaceBetween) {
                InfoItem("ENB:", lteInfo.eci.takeIf { it != Int.MAX_VALUE }?.toString() ?: "N/A")
                InfoItem("CELLID:", lteInfo.ci.takeIf { it != Int.MAX_VALUE }?.toString() ?: "N/A")
            }
            Spacer(modifier = Modifier.height(4.dp))
            Row(Modifier.fillMaxWidth(), horizontalArrangement = Arrangement.SpaceBetween) {
                InfoItem("频点:", lteInfo.earfcn.takeIf { it != Int.MAX_VALUE }?.toString() ?: "N/A")
                InfoItem("PCI:", lteInfo.pci.takeIf { it != Int.MAX_VALUE }?.toString() ?: "N/A")
                InfoItem("TAC:", lteInfo.tac.takeIf { it != Int.MAX_VALUE }?.toString() ?: "N/A")
            }
            Spacer(modifier = Modifier.height(4.dp))
            Row(Modifier.fillMaxWidth(), horizontalArrangement = Arrangement.SpaceBetween) {
                InfoItem("RSRP:", lteInfo.rsrp.takeIf { it != Int.MAX_VALUE }?.toString() ?: "N/A")
                InfoItem("RSRQ:", lteInfo.rsrq.takeIf { it != Int.MAX_VALUE }?.toString() ?: "N/A")
                InfoItem("SINR:", lteInfo.sinr.takeIf { it != Int.MAX_VALUE }?.toString() ?: "N/A")
            }
            Spacer(modifier = Modifier.height(4.dp))
            Row(Modifier.fillMaxWidth(), horizontalArrangement = Arrangement.SpaceBetween) {
                InfoItem("UL:", "0.694Mbps")
                InfoItem("DL:", "0.414Mbps")
            }

            Spacer(modifier = Modifier.height(12.dp))
            SignalTypeToggle() // RSRP/SINR Toggle
        }
    }
}

@Composable
fun NrInfoPanel(
    nrInfo: wlmwwx.duckdns.org.flytest.logic.model.RadioInfoModel.NrInfo,
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(modifier = Modifier.padding(12.dp)) {
            Text("5G NR 信息", style = MaterialTheme.typography.titleMedium, fontWeight = FontWeight.Bold)
            Spacer(modifier = Modifier.height(8.dp))
            Divider()
            Spacer(modifier = Modifier.height(12.dp))
            Row(Modifier.fillMaxWidth(), horizontalArrangement = Arrangement.SpaceBetween) {
                InfoItem("MCC:", nrInfo.mcc)
                InfoItem("MNC:", nrInfo.mnc)
                InfoItem("NCI:", nrInfo.nci.takeIf { it != Long.MAX_VALUE }?.toString() ?: "N/A")
            }
            Row(Modifier.fillMaxWidth(), horizontalArrangement = Arrangement.SpaceBetween) {
                InfoItem("TAC:", nrInfo.tac.takeIf { it != Int.MAX_VALUE }?.toString() ?: "N/A")
                InfoItem("PCI:", nrInfo.pci.takeIf { it != Int.MAX_VALUE }?.toString() ?: "N/A")
                InfoItem("NRARFCN:", nrInfo.nrarfcn.takeIf { it != Int.MAX_VALUE }?.toString() ?: "N/A")
            }
            Row(Modifier.fillMaxWidth(), horizontalArrangement = Arrangement.SpaceBetween) {
                InfoItem("CSI RSRP:", nrInfo.csi_rsrp.takeIf { it != Int.MAX_VALUE }?.toString() ?: "N/A")
                InfoItem("CSI RSRQ:", nrInfo.csi_rsrq.takeIf { it != Int.MAX_VALUE }?.toString() ?: "N/A")
                InfoItem("CSI SINR:", nrInfo.csi_sinr.takeIf { it != Int.MAX_VALUE }?.toString() ?: "N/A")
            }
            Row(Modifier.fillMaxWidth(), horizontalArrangement = Arrangement.SpaceBetween) {
                InfoItem("SS RSRP:", nrInfo.ss_rsrp.takeIf { it != Int.MAX_VALUE }?.toString() ?: "N/A")
                InfoItem("SS RSRQ:", nrInfo.ss_rsrq.takeIf { it != Int.MAX_VALUE }?.toString() ?: "N/A")
                InfoItem("SS SINR:", nrInfo.ss_sinr.takeIf { it != Int.MAX_VALUE }?.toString() ?: "N/A")
            }
        }
    }
}

@Composable
fun WcdmaInfoPanel(
    wcdmaInfo: wlmwwx.duckdns.org.flytest.logic.model.RadioInfoModel.WcdmaInfo,
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(modifier = Modifier.padding(12.dp)) {
            Text("WCDMA 信息", style = MaterialTheme.typography.titleMedium, fontWeight = FontWeight.Bold)
            Spacer(modifier = Modifier.height(8.dp))
            Divider()
            Spacer(modifier = Modifier.height(12.dp))
            Row(Modifier.fillMaxWidth(), horizontalArrangement = Arrangement.SpaceBetween) {
                InfoItem("MCC:", wcdmaInfo.mcc.takeIf { it != Int.MAX_VALUE }?.toString() ?: "N/A")
                InfoItem("MNC:", wcdmaInfo.mnc.takeIf { it != Int.MAX_VALUE }?.toString() ?: "N/A")
                InfoItem("CID:", wcdmaInfo.cid.takeIf { it != Int.MAX_VALUE }?.toString() ?: "N/A")
            }
            Row(Modifier.fillMaxWidth(), horizontalArrangement = Arrangement.SpaceBetween) {
                InfoItem("LAC:", wcdmaInfo.lac.takeIf { it != Int.MAX_VALUE }?.toString() ?: "N/A")
                InfoItem("PSC:", wcdmaInfo.psc.takeIf { it != Int.MAX_VALUE }?.toString() ?: "N/A")
                InfoItem("UARFCN:", wcdmaInfo.uarfcn.takeIf { it != Int.MAX_VALUE }?.toString() ?: "N/A")
            }
            Row(Modifier.fillMaxWidth(), horizontalArrangement = Arrangement.SpaceBetween) {
                InfoItem("RSSI:", wcdmaInfo.rssi.takeIf { it != Int.MAX_VALUE }?.toString() ?: "N/A")
                InfoItem("RxLev:", wcdmaInfo.rxlev.takeIf { it != Int.MAX_VALUE }?.toString() ?: "N/A")
            }
        }
    }
}

@Composable
fun GsmInfoPanel(
    gsmInfo: wlmwwx.duckdns.org.flytest.logic.model.RadioInfoModel.GsmInfo,
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(modifier = Modifier.padding(12.dp)) {
            Text("GSM 信息", style = MaterialTheme.typography.titleMedium, fontWeight = FontWeight.Bold)
            Spacer(modifier = Modifier.height(8.dp))
            Divider()
            Spacer(modifier = Modifier.height(12.dp))
            Row(Modifier.fillMaxWidth(), horizontalArrangement = Arrangement.SpaceBetween) {
                InfoItem("MCC:", gsmInfo.mcc.takeIf { it != Int.MAX_VALUE }?.toString() ?: "N/A")
                InfoItem("MNC:", gsmInfo.mnc.takeIf { it != Int.MAX_VALUE }?.toString() ?: "N/A")
                InfoItem("CID:", gsmInfo.cid.takeIf { it != Int.MAX_VALUE }?.toString() ?: "N/A")
            }
            Row(Modifier.fillMaxWidth(), horizontalArrangement = Arrangement.SpaceBetween) {
                InfoItem("LAC:", gsmInfo.lac.takeIf { it != Int.MAX_VALUE }?.toString() ?: "N/A")
                InfoItem("ARFCN:", gsmInfo.arfcn.takeIf { it != Int.MAX_VALUE }?.toString() ?: "N/A")
                InfoItem("BSIC:", gsmInfo.bsic.takeIf { it != Int.MAX_VALUE }?.toString() ?: "N/A")
            }
            Row(Modifier.fillMaxWidth(), horizontalArrangement = Arrangement.SpaceBetween) {
                InfoItem("RSSI:", gsmInfo.rssi.takeIf { it != Int.MAX_VALUE }?.toString() ?: "N/A")
                InfoItem("RxLev:", gsmInfo.rxlev.takeIf { it != Int.MAX_VALUE }?.toString() ?: "N/A")
            }
        }
    }
}

@Composable
fun LocationInfoPanel(locationInfo: wlmwwx.duckdns.org.flytest.logic.model.LocationInfoModel.LocationInfo) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(modifier = Modifier.padding(12.dp)) {
            Text("定位信息", style = MaterialTheme.typography.titleMedium, fontWeight = FontWeight.Bold)
            Spacer(modifier = Modifier.height(8.dp))
            Divider()
            Spacer(modifier = Modifier.height(12.dp))
            Row(Modifier.fillMaxWidth(), horizontalArrangement = Arrangement.SpaceBetween) {
                InfoItem("经度:", locationInfo.longitude.toString())
                InfoItem("纬度:", locationInfo.latitude.toString())

            }
            Row(Modifier.fillMaxWidth(), horizontalArrangement = Arrangement.SpaceBetween) {
                InfoItem("高度:", locationInfo.altitude.toString())
                InfoItem("速度:", locationInfo.speed.toString())
            }
            Row(Modifier.fillMaxWidth(), horizontalArrangement = Arrangement.SpaceBetween) {
                InfoItem("时间:", locationInfo.time.toString())
            }
        }
    }
}

@Composable
fun InfoItem(label: String, value: String, modifier: Modifier = Modifier) {
    Row(modifier = modifier, verticalAlignment = Alignment.CenterVertically) {
        Text(
            text = label,
            style = MaterialTheme.typography.bodySmall,
            color = Color.Gray,
            modifier = Modifier.width(50.dp) // Adjust width as needed for alignment
        )
        Text(text = value, style = MaterialTheme.typography.bodyMedium, fontWeight = FontWeight.Medium)
    }
}

@Composable
fun SignalTypeToggle() {
    var selectedOption by remember { mutableStateOf("RSRP") }
    Row(verticalAlignment = Alignment.CenterVertically) {
        Row(verticalAlignment = Alignment.CenterVertically) {
            RadioButton(
                selected = selectedOption == "RSRP",
                onClick = { selectedOption = "RSRP" },
                colors = RadioButtonDefaults.colors(selectedColor = MaterialTheme.colorScheme.primary)
            )
            Text("RSRP", style = MaterialTheme.typography.bodyMedium)
        }
        Spacer(modifier = Modifier.width(16.dp))
        Row(verticalAlignment = Alignment.CenterVertically) {
            RadioButton(
                selected = selectedOption == "SINR",
                onClick = { selectedOption = "SINR" },
                colors = RadioButtonDefaults.colors(selectedColor = MaterialTheme.colorScheme.primary)
            )
            Text("SINR", style = MaterialTheme.typography.bodyMedium)
        }
         Spacer(modifier = Modifier.width(16.dp))
         Text("图例", style = MaterialTheme.typography.bodyMedium, color = Color.Gray) // Legend
         // TODO: Add Arrow Up icon if needed
    }
}


@Composable
fun StatItem(label: String, value: String) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 8.dp),
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        Text(
            text = label,
            style = MaterialTheme.typography.bodyLarge,
            color = Color.Gray
        )
        Text(
            text = value,
            style = MaterialTheme.typography.bodyLarge,
            fontWeight = FontWeight.Medium
        )
    }
}

@Composable
fun BottomControls() {
    BottomAppBar(
        containerColor = MaterialTheme.colorScheme.primaryContainer // Or another suitable color
    ) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceAround,
            verticalAlignment = Alignment.CenterVertically
        ) {
            IconButton(onClick = { /* TODO: Handle settings click */ }) {
                Icon(Icons.Filled.Settings, contentDescription = "Settings", tint = MaterialTheme.colorScheme.onPrimaryContainer)
            }
            IconButton(onClick = { /* TODO: Handle play click */ }) {
                Icon(Icons.Filled.PlayArrow, contentDescription = "Play", tint = MaterialTheme.colorScheme.onPrimaryContainer)
            }
            IconButton(onClick = { /* TODO: Handle refresh click */ }) {
                Icon(Icons.Filled.Refresh, contentDescription = "Refresh", tint = MaterialTheme.colorScheme.onPrimaryContainer)
            }
        }
    }
}
