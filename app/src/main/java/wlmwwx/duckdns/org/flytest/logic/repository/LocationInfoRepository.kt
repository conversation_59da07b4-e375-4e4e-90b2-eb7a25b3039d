package wlmwwx.duckdns.org.flytest.logic.repository

import android.content.Context
import wlmwwx.duckdns.org.flytest.logic.model.LocationInfoModel
import wlmwwx.duckdns.org.flytest.logic.dao.LocationInfoDao

class LocationInfoRepository(context: Context) {
    private val model = LocationInfoModel(context)
    private val dao = LocationInfoDao()

    fun startLocationUpdates() {
        model.startLocationUpdates()
    }

    fun stopLocationUpdates() {
        model.stopLocationUpdates()
    }

    fun setLocationInfoListener(listener: LocationInfoModel.LocationInfoListener?) {
        model.listener = listener
    }

    // For backward compatibility and immediate access to current location
    fun getLocationInfo(): LocationInfoModel.LocationInfo {
        val info = model.locationInfo
        dao.saveLocationInfo(info)
        return info
    }
} 