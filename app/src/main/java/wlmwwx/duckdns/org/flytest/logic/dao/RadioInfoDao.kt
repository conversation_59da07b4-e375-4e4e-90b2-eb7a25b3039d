package wlmwwx.duckdns.org.flytest.logic.dao

import wlmwwx.duckdns.org.flytest.logic.model.RadioInfoModel

class RadioInfoDao {
    // In-memory cache for RadioInfo data
    var lteInfo: RadioInfoModel.LteInfo? = null
    // ... (other network info fields as needed)

    fun saveLteInfo(info: RadioInfoModel.LteInfo) {
        lteInfo = info
    }
    fun getLteInfo(): RadioInfoModel.LteInfo? = lteInfo
    // ... (other get/set methods)
} 