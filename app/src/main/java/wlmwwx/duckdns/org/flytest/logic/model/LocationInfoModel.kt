package wlmwwx.duckdns.org.flytest.logic.model

import android.Manifest
import android.content.Context
import android.location.Criteria
import android.location.Location
import android.location.LocationListener
import android.location.LocationManager
import android.os.Bundle
import android.os.Handler
import android.util.Log
import androidx.annotation.RequiresPermission
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import java.util.*

class LocationInfoModel(context: Context) {
    data class LocationInfo(
        val longitude: Double = 0.0,
        val latitude: Double = 0.0,
        val altitude: Double = 0.0,
        val speed: Float = 0f,
        val time: Date = Date()
    )

    interface LocationInfoListener {
        fun onLocationUpdated(info: LocationInfo)
    }

    var listener: LocationInfoListener? = null

    var locationInfo by mutableStateOf(LocationInfo())
        private set

    private val mLocationMgr: LocationManager = context.getSystemService(Context.LOCATION_SERVICE) as LocationManager
    private val mCriteria = Criteria().apply {
        accuracy = Criteria.ACCURACY_FINE
        isAltitudeRequired = true
        isBearingRequired = true
        isCostAllowed = true
        powerRequirement = Criteria.POWER_LOW
    }
    private val mHandler = Handler()
    private var bLocationEnable = false
    private val tag = "LocationInfoModel"

    private val mRefresh = object : Runnable {
        @RequiresPermission(allOf = [Manifest.permission.ACCESS_FINE_LOCATION, Manifest.permission.ACCESS_COARSE_LOCATION])
        override fun run() {
            if (!bLocationEnable) {
                initLocation()
                mHandler.postDelayed(this, 1000)
            }
        }
    }

    fun startLocationUpdates() {
        initLocation()
        mHandler.postDelayed(mRefresh, 100)
    }

    fun stopLocationUpdates() {
        mHandler.removeCallbacks(mRefresh)
        mLocationMgr.removeUpdates(mLocationListener)
        bLocationEnable = false
    }

    @RequiresPermission(allOf = [Manifest.permission.ACCESS_FINE_LOCATION, Manifest.permission.ACCESS_COARSE_LOCATION])
    private fun initLocation() {
        var bestProvider = mLocationMgr.getBestProvider(mCriteria, true)
        if (bestProvider == null) {
            bestProvider = LocationManager.NETWORK_PROVIDER
        }
        if (mLocationMgr.isProviderEnabled(bestProvider)) {
            Log.d(tag, "\n正在获取${bestProvider}定位对象")
            beginLocation(bestProvider)
            bLocationEnable = true
        } else {
            Log.d(tag, "\n${bestProvider}定位不可用")
            bLocationEnable = false
        }
    }

    @RequiresPermission(allOf = [Manifest.permission.ACCESS_FINE_LOCATION, Manifest.permission.ACCESS_COARSE_LOCATION])
    private fun beginLocation(method: String) {
        mLocationMgr.requestLocationUpdates(method, 300, 0f, mLocationListener)
        mLocationMgr.getLastKnownLocation(method)?.let { updateLocationInfo(it) }
    }

    private fun updateLocationInfo(location: Location?) {
        if (location != null) {
            val newInfo = LocationInfo(
                longitude = location.longitude,
                latitude = location.latitude,
                altitude = location.altitude,
                speed = location.speed,
                time = Date()
            )
            locationInfo = newInfo
            listener?.onLocationUpdated(newInfo)
            Log.d(tag, "Location updated: $locationInfo")
        } else {
            Log.d(tag, "No location available")
        }
    }

    private val mLocationListener = object : LocationListener {
        override fun onLocationChanged(location: Location) {
            updateLocationInfo(location)
        }
        override fun onProviderDisabled(provider: String) {}
        override fun onProviderEnabled(provider: String) {}
        override fun onStatusChanged(provider: String, status: Int, extras: Bundle) {}
    }

    fun getLongitude(): Double = locationInfo.longitude
    fun getLatitude(): Double = locationInfo.latitude
    fun getAltitude(): Double = locationInfo.altitude
    fun getSpeed(): Float = locationInfo.speed
} 