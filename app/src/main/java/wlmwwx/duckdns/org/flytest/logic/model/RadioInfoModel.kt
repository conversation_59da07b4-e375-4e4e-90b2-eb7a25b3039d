package wlmwwx.duckdns.org.flytest.logic.model

import android.content.Context
import android.telephony.*
import android.util.Log
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import java.lang.reflect.Modifier
import android.telephony.TelephonyCallback
import android.telephony.CellInfo
import android.telephony.CellSignalStrength
import android.os.Build
import android.telephony.PhoneStateListener

class RadioInfoModel(context: Context) {
    data class LteInfo(
        var mcc: Int = Int.MAX_VALUE,
        var mnc: Int = Int.MAX_VALUE,
        var ci: Int = Int.MAX_VALUE,
        var pci: Int = Int.MAX_VALUE,
        var tac: Int = Int.MAX_VALUE,
        var rsrp: Int = Int.MAX_VALUE,
        var rsrq: Int = Int.MAX_VALUE,
        var sinr: Int = Int.MAX_VALUE,
        var eci: Int = Int.MAX_VALUE,
        var earfcn: Int = Int.MAX_VALUE,
        var bandWidth: Int = Int.MAX_VALUE,
        var rssi: Int = Int.MAX_VALUE,
        var cqi: Int = Int.MAX_VALUE
    )
    data class CdmaInfo(
        var mcc: Int = Int.MAX_VALUE,
        var mnc: Int = Int.MAX_VALUE,
        var sid: Int = Int.MAX_VALUE,
        var nid: Int = Int.MAX_VALUE,
        var bsid: Int = Int.MAX_VALUE,
        var rxPwr: Int = Int.MAX_VALUE,
        var ecIo: Float = Float.MAX_VALUE,
        var evdoRxPwr: Int = Int.MAX_VALUE,
        var evdoEcIo: Float = Float.MAX_VALUE,
        var evdoSnr: Int = Int.MAX_VALUE
    )
    data class GsmInfo(
        var mcc: Int = Int.MAX_VALUE,
        var mnc: Int = Int.MAX_VALUE,
        var lac: Int = Int.MAX_VALUE,
        var cid: Int = Int.MAX_VALUE,
        var rssi: Int = Int.MAX_VALUE,
        var rxlev: Int = Int.MAX_VALUE,
        var arfcn: Int = Int.MAX_VALUE,
        var bsic: Int = Int.MAX_VALUE
    )
    data class WcdmaInfo(
        var mcc: Int = Int.MAX_VALUE,
        var mnc: Int = Int.MAX_VALUE,
        var lac: Int = Int.MAX_VALUE,
        var cid: Int = Int.MAX_VALUE,
        var psc: Int = Int.MAX_VALUE,
        var rssi: Int = Int.MAX_VALUE,
        var rxlev: Int = Int.MAX_VALUE,
        var uarfcn: Int = Int.MAX_VALUE
    )
    data class NrInfo(
        var mcc: String = "-",
        var mnc: String = "-",
        var tac: Int = Int.MAX_VALUE,
        var pci: Int = Int.MAX_VALUE,
        var nci: Long = Long.MAX_VALUE,
        var nrarfcn: Int = Int.MAX_VALUE,
        var csi_rsrp: Int = Int.MAX_VALUE,
        var csi_rsrq: Int = Int.MAX_VALUE,
        var csi_sinr: Int = Int.MAX_VALUE,
        var ss_rsrp: Int = Int.MAX_VALUE,
        var ss_rsrq: Int = Int.MAX_VALUE,
        var ss_sinr: Int = Int.MAX_VALUE
    )

    interface RadioInfoListener {
        fun onLteInfoUpdated(info: LteInfo) {}
        fun onNrInfoUpdated(info: NrInfo) {}
        fun onWcdmaInfoUpdated(info: WcdmaInfo) {}
        fun onGsmInfoUpdated(info: GsmInfo) {}
        fun onCdmaInfoUpdated(info: CdmaInfo) {}
        fun onCellTypeUpdated(type: String) {}
    }
    var listener: RadioInfoListener? = null

    var lte by mutableStateOf(LteInfo())
    var cdma by mutableStateOf(CdmaInfo())
    var gsm by mutableStateOf(GsmInfo())
    var wcdma by mutableStateOf(WcdmaInfo())
    var nr by mutableStateOf(NrInfo())
    var cellType by mutableStateOf("NONE")

    private val mcontext: Context = context
    private val mTM: TelephonyManager = mcontext.getSystemService(Context.TELEPHONY_SERVICE) as TelephonyManager
    private val tag: String = RadioInfoModel::class.java.name

    private var telephonyCallback: TelephonyCallback? = null
    private var phoneStateListener: PhoneStateListener? = null

    fun registerCallbacks() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            telephonyCallback = object : TelephonyCallback(),
                TelephonyCallback.CellInfoListener,
                TelephonyCallback.SignalStrengthsListener {
                override fun onCellInfoChanged(cellInfo: List<CellInfo>) {
                    getCellIdentity()
                }
                override fun onSignalStrengthsChanged(signalStrength: SignalStrength) {
                    mTM.allCellInfo?.forEach { cellInfo ->
                        when (cellInfo) {
                            is CellInfoLte -> getSignalStrength(cellInfo.cellSignalStrength)
                            is CellInfoNr -> getSignalStrength(cellInfo.cellSignalStrength)
                            is CellInfoGsm -> getSignalStrength(cellInfo.cellSignalStrength)
                            is CellInfoWcdma -> getSignalStrength(cellInfo.cellSignalStrength)
                            is CellInfoCdma -> getSignalStrength(cellInfo.cellSignalStrength)
                        }
                    }
                }
            }
            mTM.registerTelephonyCallback(mcontext.mainExecutor, telephonyCallback as TelephonyCallback)
        } else {
            phoneStateListener = object : PhoneStateListener() {
                override fun onCellInfoChanged(cellInfo: List<CellInfo>) {
                    getCellIdentity()
                }
                override fun onSignalStrengthsChanged(signalStrength: SignalStrength) {
                    mTM.allCellInfo?.forEach { cellInfo ->
                        when (cellInfo) {
                            is CellInfoLte -> getSignalStrength(cellInfo.cellSignalStrength)
                            is CellInfoNr -> getSignalStrength(cellInfo.cellSignalStrength)
                            is CellInfoGsm -> getSignalStrength(cellInfo.cellSignalStrength)
                            is CellInfoWcdma -> getSignalStrength(cellInfo.cellSignalStrength)
                            is CellInfoCdma -> getSignalStrength(cellInfo.cellSignalStrength)
                        }
                    }
                }
            }
            mTM.listen(phoneStateListener, PhoneStateListener.LISTEN_CELL_INFO or PhoneStateListener.LISTEN_SIGNAL_STRENGTHS)
        }
    }

    fun unregisterCallbacks() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            telephonyCallback?.let {
                mTM.unregisterTelephonyCallback(it)
            }
        } else {
            phoneStateListener?.let {
                mTM.listen(it, PhoneStateListener.LISTEN_NONE)
            }
        }
    }

    fun radioInfoExit() {
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.S) {
            try {
                // mTelephonyCallback should be managed if you implement callback registration
            } catch (e: Exception) {
                Log.e(tag, "Error unregistering TelephonyCallback", e)
            }
        } else {
            // mTelephonyCallback should be managed if you implement callback registration
        }
    }

    fun getAllCellInfo(): String {
        Log.d(tag, "getAllCellInfo start")
        getCellIdentity()
        val infoString = when(cellType) {
            "LTE" -> "LTE Info: MCC=${lte.mcc}, MNC=${lte.mnc}, CI=${lte.ci}, TAC=${lte.tac}, RSRP=${lte.rsrp}, RSRQ=${lte.rsrq}, SINR=${lte.sinr}"
            "CDMA" -> "CDMA Info: SID=${cdma.sid}, NID=${cdma.nid}, BSID=${cdma.bsid}, RxPwr=${cdma.rxPwr}, EcIo=${cdma.ecIo}"
            "GSM" -> "GSM Info: MCC=${gsm.mcc}, MNC=${gsm.mnc}, CID=${gsm.cid}, LAC=${gsm.lac}, RSSI=${gsm.rssi}, RxLev=${gsm.rxlev}"
            "WCDMA" -> "WCDMA Info: MCC=${wcdma.mcc}, MNC=${wcdma.mnc}, CID=${wcdma.cid}, LAC=${wcdma.lac}, PSC=${wcdma.psc}, RSSI=${wcdma.rssi}, RxLev=${wcdma.rxlev}"
            "NR" -> "NR Info: MCC=${nr.mcc}, MNC=${nr.mnc}, NCI=${nr.nci}, TAC=${nr.tac}, PCI=${nr.pci}, CSI RSRP=${nr.csi_rsrp}, CSI RSRQ=${nr.csi_rsrq}, CSI SINR=${nr.csi_sinr}, SS RSRP=${nr.ss_rsrp}, SS RSRQ=${nr.ss_rsrq}, SS SINR=${nr.ss_sinr}"
            else -> "No cell info available"
        }
        Log.d(tag, "getAllCellInfo end: $infoString")
        return infoString
    }

    fun getSignalStrength(signal: CellSignalStrength) {
        when (signal) {
            is CellSignalStrengthLte -> {
                try {
                    val rsrpMethod = signal.javaClass.getMethod("getRsrp")
                    val rsrqMethod = signal.javaClass.getMethod("getRsrq")
                    val cqiMethod = signal.javaClass.getMethod("getCqi")
                    val rsrp = rsrpMethod.invoke(signal) as? Int ?: Int.MAX_VALUE
                    val rsrq = rsrqMethod.invoke(signal) as? Int ?: Int.MAX_VALUE
                    val cqi = cqiMethod.invoke(signal) as? Int ?: Int.MAX_VALUE
                    var sinr = Int.MAX_VALUE
                    try {
                        val sinrMethod = signal.javaClass.getMethod("getRssnr")
                        sinr = sinrMethod.invoke(signal) as? Int ?: Int.MAX_VALUE
                    } catch (e: Exception) {
                        try {
                            val sinrMethod = signal.javaClass.getMethod("getSinr")
                            sinr = sinrMethod.invoke(signal) as? Int ?: Int.MAX_VALUE
                        } catch (e: Exception) {
                            Log.e(tag, "Error getting LTE SINR: ", e)
                        }
                    }
                    lte = lte.copy(rsrp=rsrp, rsrq=rsrq, cqi=cqi, sinr=sinr)
                    if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.Q) {
                        try {
                            val rssiMethod = signal.javaClass.getMethod("getRssi")
                            val rssi = rssiMethod.invoke(signal) as? Int ?: Int.MAX_VALUE
                            lte = lte.copy(rssi=rssi)
                        } catch (e: Exception) {
                            Log.e(tag, "Error getting LTE RSSI: ", e)
                        }
                    }
                    listener?.onLteInfoUpdated(lte)
                    listener?.onCellTypeUpdated(cellType)
                } catch (e: Exception) {
                    Log.e(tag, "Error getting LTE signal strength: ", e)
                }
            }
            is CellSignalStrengthNr -> {
                nr = nr.copy(csi_rsrp=signal.csiRsrp, csi_rsrq=signal.csiRsrq, csi_sinr=signal.csiSinr,
                             ss_rsrp=signal.ssRsrp, ss_rsrq=signal.ssRsrq, ss_sinr=signal.ssSinr)
                listener?.onNrInfoUpdated(nr)
                listener?.onCellTypeUpdated(cellType)
            }
            is CellSignalStrengthGsm -> {
                try {
                    val rssiMethod = signal.javaClass.getMethod("getRssi")
                    val rssi = rssiMethod.invoke(signal) as? Int ?: Int.MAX_VALUE
                    var rxlev = Int.MAX_VALUE
                    try {
                        val rxlevMethod = signal.javaClass.getMethod("getRxlev")
                        rxlev = rxlevMethod.invoke(signal) as? Int ?: Int.MAX_VALUE
                    } catch (e: Exception) {
                        rxlev = signal.dbm
                    }
                    gsm = gsm.copy(rssi=rssi, rxlev=rxlev)
                    listener?.onGsmInfoUpdated(gsm)
                    listener?.onCellTypeUpdated(cellType)
                } catch (e: Exception) {
                    Log.e(tag, "Error getting GSM signal strength: ", e)
                    gsm = gsm.copy(rssi=signal.dbm, rxlev=signal.dbm)
                    listener?.onGsmInfoUpdated(gsm)
                    listener?.onCellTypeUpdated(cellType)
                }
            }
            is CellSignalStrengthWcdma -> {
                try {
                    val rssiMethod = signal.javaClass.getMethod("getRssi")
                    val rssi = rssiMethod.invoke(signal) as? Int ?: Int.MAX_VALUE
                    wcdma = wcdma.copy(rssi=rssi, rxlev=signal.dbm)
                    listener?.onWcdmaInfoUpdated(wcdma)
                    listener?.onCellTypeUpdated(cellType)
                } catch (e: Exception) {
                    Log.e(tag, "Error getting WCDMA signal strength: ", e)
                    wcdma = wcdma.copy(rssi=signal.dbm, rxlev=signal.dbm)
                    listener?.onWcdmaInfoUpdated(wcdma)
                    listener?.onCellTypeUpdated(cellType)
                }
            }
            is CellSignalStrengthCdma -> {
                cdma = cdma.copy(rxPwr=signal.cdmaDbm, ecIo=signal.cdmaEcio.toFloat(),
                                 evdoRxPwr=signal.evdoDbm, evdoEcIo=signal.evdoEcio.toFloat(), evdoSnr=signal.evdoSnr)
                listener?.onCdmaInfoUpdated(cdma)
                listener?.onCellTypeUpdated(cellType)
            }
            else -> {}
        }
    }

    private fun getCellIdentity() {
        mTM.allCellInfo?.forEach { cellInfo ->
            if (!cellInfo.isRegistered) return@forEach
            when (cellInfo) {
                is CellInfoLte -> cellInfo.cellIdentity.let { identity ->
                    lte = lte.copy(
                        mcc = if (identity.mcc == Int.MAX_VALUE) Int.MAX_VALUE else identity.mcc,
                        mnc = if (identity.mnc == Int.MAX_VALUE) Int.MAX_VALUE else identity.mnc,
                        ci = identity.ci,
                        pci = identity.pci,
                        tac = identity.tac,
                        eci = identity.ci
                    )
                    if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.N) {
                        lte = lte.copy(earfcn = identity.earfcn)
                    }
                    if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.P) {
                        lte = lte.copy(bandWidth = identity.bandwidth)
                    }
                    cellType = "LTE"
                    listener?.onLteInfoUpdated(lte)
                    listener?.onCellTypeUpdated(cellType)
                }
                is CellInfoCdma -> cellInfo.cellIdentity.let { identity ->
                    cdma = cdma.copy(
                        sid = identity.systemId,
                        nid = identity.networkId,
                        bsid = identity.basestationId
                    )
                    if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.P) {
                        try {
                            val mccMethod = identity.javaClass.getMethod("getMcc")
                            val mncMethod = identity.javaClass.getMethod("getMnc")
                            val mcc = mccMethod.invoke(identity) as? Int ?: Int.MAX_VALUE
                            val mnc = mncMethod.invoke(identity) as? Int ?: Int.MAX_VALUE
                            cdma = cdma.copy(
                                mcc = if (mcc == Int.MAX_VALUE) Int.MAX_VALUE else mcc,
                                mnc = if (mnc == Int.MAX_VALUE) Int.MAX_VALUE else mnc
                            )
                        } catch (e: Exception) {
                            Log.e(tag, "Error getting CDMA MCC/MNC: ", e)
                        }
                    }
                    cellType = "CDMA"
                    listener?.onCdmaInfoUpdated(cdma)
                    listener?.onCellTypeUpdated(cellType)
                }
                is CellInfoGsm -> cellInfo.cellIdentity.let { identity ->
                    gsm = gsm.copy(
                        mcc = if (identity.mcc == Int.MAX_VALUE) Int.MAX_VALUE else identity.mcc,
                        mnc = if (identity.mnc == Int.MAX_VALUE) Int.MAX_VALUE else identity.mnc,
                        cid = identity.cid,
                        lac = identity.lac
                    )
                    if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.N) {
                        gsm = gsm.copy(arfcn = identity.arfcn, bsic = identity.bsic)
                    }
                    cellType = "GSM"
                    listener?.onGsmInfoUpdated(gsm)
                    listener?.onCellTypeUpdated(cellType)
                }
                is CellInfoWcdma -> cellInfo.cellIdentity.let { identity ->
                    wcdma = wcdma.copy(
                        mcc = if (identity.mcc == Int.MAX_VALUE) Int.MAX_VALUE else identity.mcc,
                        mnc = if (identity.mnc == Int.MAX_VALUE) Int.MAX_VALUE else identity.mnc,
                        cid = identity.cid,
                        lac = identity.lac,
                        psc = identity.psc
                    )
                    if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.N) {
                        wcdma = wcdma.copy(uarfcn = identity.uarfcn)
                    }
                    cellType = "WCDMA"
                    listener?.onWcdmaInfoUpdated(wcdma)
                    listener?.onCellTypeUpdated(cellType)
                }
                else -> {
                    if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.Q && cellInfo is CellInfoNr) {
                        val identity = cellInfo.cellIdentity as? CellIdentityNr
                        if (identity != null) {
                            nr = nr.copy(
                                mcc = identity.mccString ?: "-",
                                mnc = identity.mncString ?: "-",
                                tac = identity.tac,
                                pci = identity.pci,
                                nci = identity.nci,
                                nrarfcn = identity.nrarfcn
                            )
                            cellType = "NR"
                            listener?.onNrInfoUpdated(nr)
                            listener?.onCellTypeUpdated(cellType)
                        }
                    }
                }
            }
        }
    }
} 