package wlmwwx.duckdns.org.flytest.utils
import android.Manifest
import android.content.Context
import android.location.Criteria
import android.location.Location
import android.location.LocationListener
import android.location.LocationManager
import android.os.Bundle
import android.os.Handler
import android.util.Log
import androidx.annotation.RequiresPermission
import java.util.*

class GetLosition(private val context: Context, myLositionListeners: MyLositionListeners) {
    companion object {
        private const val TAG = "Location"
    }

    interface MyLositionListeners {
        fun showLocation(map: HashMap<String, Any>)
    }

    interface LoadingListener {
        fun onFinishedLoading(success: Boolean)
    }

    private var mLocation = ""
    private val mLocationMgr: LocationManager = context.getSystemService(Context.LOCATION_SERVICE) as LocationManager
    private val mCriteria = Criteria().apply {
        accuracy = Criteria.ACCURACY_FINE
        isAltitudeRequired = true
        isBearingRequired = true
        isCostAllowed = true
        powerRequirement = Criteria.POWER_LOW
    }
    private val mHandler = Handler()
    private var bLocationEnable = false
    private var speed = 0f
    private var longitude = 0.0
    private var latitude = 0.0
    private var altitude = 0.0
    private var myLositionListeners: MyLositionListeners? = null
    private var mLoadingListener: LoadingListener? = null

    private val mRefresh = object : Runnable {
        @RequiresPermission(allOf = [Manifest.permission.ACCESS_FINE_LOCATION, Manifest.permission.ACCESS_COARSE_LOCATION])
        override fun run() {
            if (!bLocationEnable) {
                initLocation()
                mHandler.postDelayed(this, 1000)
            }
        }
    }

    init {
        this.myLositionListeners = myLositionListeners
        initLocation()

        mHandler.postDelayed(mRefresh, 100)
    }

    @RequiresPermission(allOf = [Manifest.permission.ACCESS_FINE_LOCATION, Manifest.permission.ACCESS_COARSE_LOCATION])
    private fun initLocation() {
        var bestProvider = mLocationMgr.getBestProvider(mCriteria, true)
        if (bestProvider == null) {
            bestProvider = LocationManager.NETWORK_PROVIDER
        }

        if (mLocationMgr.isProviderEnabled(bestProvider)) {
            Log.d(TAG, "\n正在获取${bestProvider}定位对象")
            mLocation = "\n定位类型=$bestProvider"
            beginLocation(bestProvider)
            bLocationEnable = true
        } else {
            Log.d(TAG, "\n${bestProvider}定位不可用")
            bLocationEnable = false
        }
    }

    @RequiresPermission(allOf = [Manifest.permission.ACCESS_FINE_LOCATION, Manifest.permission.ACCESS_COARSE_LOCATION])
    private fun beginLocation(method: String) {
        mLocationMgr.requestLocationUpdates(method, 300, 0f, mLocationListener)
        mLocationMgr.getLastKnownLocation(method)?.let { setLocationText(it) }
    }

    private fun setLocationText(location: Location?) {
        val map = HashMap<String, Any>()
        if (location != null) {
            val desc = """
                $mLocation
                定位对象信息如下：
                    其中时间：${Date()}
                    其中经度：${location.longitude}
                    其中纬度：${location.latitude}
                    其中高度：${location.altitude}
            """.trimIndent()

            longitude = location.longitude
            latitude = location.latitude
            altitude = location.altitude
            speed = location.speed

            if (speed > 0) {
                map["speed"] = speed
                speed = 0f // 清除速度
            }

            map.apply {
                put("mLocation", mLocation)
                put("Longitude", longitude)
                put("Latitude", latitude)
                put("Altitude", altitude)
                put("speed", speed)
            }

            myLositionListeners?.showLocation(map)
            Log.d(TAG, desc)
        } else {
            Log.d(TAG, "$mLocation\n暂未获取到定位对象")
        }
    }

    private val mLocationListener = object : LocationListener {
        override fun onLocationChanged(location: Location) {
            longitude = location.longitude
            latitude = location.latitude
            altitude = location.altitude

            val map = HashMap<String, Any>().apply {
                put("mLocation", mLocation)
                put("Longitude", longitude)
                put("getLatitude", latitude)
                put("Altitude", altitude)
            }

            setLocationText(location)
        }

        override fun onProviderDisabled(provider: String) {}
        override fun onProviderEnabled(provider: String) {}
        override fun onStatusChanged(provider: String, status: Int, extras: Bundle) {}
    }



    fun setLoadingListener(listener: LoadingListener) {
        mLoadingListener = listener
    }

    fun getLongitude(): Double = longitude
    fun getLatitude(): Double = latitude
    fun getAltitude(): Double = altitude
}