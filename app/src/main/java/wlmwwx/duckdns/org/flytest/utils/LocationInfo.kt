package wlmwwx.duckdns.org.flytest.utils
import android.Manifest
import android.content.Context
import android.location.Criteria
import android.location.Location
import android.location.LocationListener
import android.location.LocationManager
import android.os.Bundle
import android.os.Handler
import android.util.Log
import androidx.annotation.RequiresPermission
import java.util.*

class GetLosition(private val context: Context, myLositionListeners: MyLositionListeners) {
    companion object {
        private const val TAG = "Location"
        private const val TWO_MINUTES = 1000 * 60 * 2 // 定义两分钟的常量
    }

    interface MyLositionListeners {
        fun showLocation(map: HashMap<String, Any>)
    }

    interface LoadingListener {
        fun onFinishedLoading(success: Boolean)
    }

    private var mLocation = ""
    private val mLocationMgr: LocationManager = context.getSystemService(Context.LOCATION_SERVICE) as LocationManager
    private val mCriteria = Criteria().apply {
        accuracy = Criteria.ACCURACY_FINE
        isAltitudeRequired = true
        isBearingRequired = true
        isCostAllowed = true
        powerRequirement = Criteria.POWER_LOW // 考虑调整此项以更好地配合ACCURACY_FINE，但暂时保留
    }
    private val mHandler = Handler()
    private var bLocationEnable = false // This flag will be removed or repurposed
    private var speed = 0f
    private var longitude = 0.0
    private var latitude = 0.0
    private var altitude = 0.0
    private var myLositionListeners: MyLositionListeners? = null
    private var mLoadingListener: LoadingListener? = null
    private var currentBestLocation: Location? = null // Added to keep track of the best location

    private val mRefresh = object : Runnable { // This Runnable will be removed
        @RequiresPermission(allOf = [Manifest.permission.ACCESS_FINE_LOCATION, Manifest.permission.ACCESS_COARSE_LOCATION])
        override fun run() {
            if (!bLocationEnable) {
                initLocation()
                mHandler.postDelayed(this, 1000)
            }
        }
    }

    init {
        this.myLositionListeners = myLositionListeners
        // Removed initial call to initLocation here
        initLocation() // Call initLocation to start requesting updates
        // Removed the mRefresh postDelayed call
    }

    @RequiresPermission(allOf = [Manifest.permission.ACCESS_FINE_LOCATION, Manifest.permission.ACCESS_COARSE_LOCATION])
    private fun initLocation() {
        // Instead of getting the best provider, request updates from both GPS and Network
        try {
            // Request updates from GPS provider
            if (mLocationMgr.isProviderEnabled(LocationManager.GPS_PROVIDER)) {
                Log.d(TAG, "Requesting updates from GPS_PROVIDER")
                mLocationMgr.requestLocationUpdates(LocationManager.GPS_PROVIDER, 300, 0f, mLocationListener)
                mLocation = "
定位类型=GPS_PROVIDER" // Update mLocation based on provider
                // Get last known GPS location
                mLocationMgr.getLastKnownLocation(LocationManager.GPS_PROVIDER)?.let {
                     if (isBetterLocation(it, currentBestLocation)) {
                         currentBestLocation = it
                         setLocationText(it) // Process the last known location if it's better
                     }
                }
            } else {
                Log.d(TAG, "GPS_PROVIDER not enabled")
            }
        } catch (e: SecurityException) {
            Log.e(TAG, "SecurityException when requesting GPS updates", e)
            // Handle permission issues appropriately (e.g., notify user)
        }

        try {
            // Request updates from Network provider
            if (mLocationMgr.isProviderEnabled(LocationManager.NETWORK_PROVIDER)) {
                Log.d(TAG, "Requesting updates from NETWORK_PROVIDER")
                mLocationMgr.requestLocationUpdates(LocationManager.NETWORK_PROVIDER, 300, 0f, mLocationListener)
                 // Get last known Network location
                 mLocationMgr.getLastKnownLocation(LocationManager.NETWORK_PROVIDER)?.let {
                      if (isBetterLocation(it, currentBestLocation)) {
                         currentBestLocation = it
                         setLocationText(it) // Process the last known location if it's better
                      }
                 }
            } else {
                Log.d(TAG, "NETWORK_PROVIDER not enabled")
            }
        } catch (e: SecurityException) {
             Log.e(TAG, "SecurityException when requesting Network updates", e)
             // Handle permission issues appropriately
        }

        // If neither provider is enabled, you might want to notify the user or take other action
        if (!mLocationMgr.isProviderEnabled(LocationManager.GPS_PROVIDER) && !mLocationMgr.isProviderEnabled(LocationManager.NETWORK_PROVIDER)) {
             Log.d(TAG, "Neither GPS nor Network provider is enabled.")
             // Optionally, notify the listener that location services are unavailable
        }
    }

    @RequiresPermission(allOf = [Manifest.permission.ACCESS_FINE_LOCATION, Manifest.permission.ACCESS_COARSE_LOCATION])
    private fun beginLocation(method: String) { // This method is no longer directly used in initLocation
        mLocationMgr.requestLocationUpdates(method, 300, 0f, mLocationListener)
        mLocationMgr.getLastKnownLocation(method)?.let { setLocationText(it) }
    }

    private fun setLocationText(location: Location?) { // This method now processes the *best* location
        if (location != null) {
            // Update the mLocation string based on the provider that gave the best location
            mLocation = "
定位类型=${location.provider}"

            val desc = """
                $mLocation
                定位对象信息如下：
                    其中时间：${Date(location.time)} // Use location time
                    其中经度：${location.longitude}
                    其中纬度：${location.latitude}
                    其中高度：${location.altitude}
                    其中速度：${location.speed} // Include speed here
                    其中精度：${location.accuracy} // Include accuracy
            """.trimIndent()

            longitude = location.longitude
            latitude = location.latitude
            altitude = location.altitude
            speed = location.speed // Update speed from location

            val map = HashMap<String, Any>().apply {
                put("mLocation", mLocation)
                put("Longitude", longitude)
                put("Latitude", latitude) // Corrected typo from getLatitude
                put("Altitude", altitude)
                put("speed", speed)
                put("accuracy", location.accuracy) // Add accuracy to map
            }

            myLositionListeners?.showLocation(map)
            Log.d(TAG, desc)
        } else {
            Log.d(TAG, "$mLocation
暂未获取到定位对象") // This case should be less frequent with proper handling
        }
    }

    private val mLocationListener = object : LocationListener {
        override fun onLocationChanged(location: Location) {
            // Logic to determine if the new location is better than the current best
            if (isBetterLocation(location, currentBestLocation)) {
                currentBestLocation = location
                // Update member variables and notify listener using the best location
                setLocationText(currentBestLocation)
            }
        }

        override fun onProviderDisabled(provider: String) {}
        override fun onProviderEnabled(provider: String) {}
        override fun onStatusChanged(provider: String, status: Int, extras: Bundle) {}
    }

    // Helper method to determine if newLocation is better than currentBestLocation
    // Based on https://developer.android.com/guide/topics/location/strategies
    private fun isBetterLocation(location: Location, currentBestLocation: Location?): Boolean {
        if (currentBestLocation == null) {
            // A new location is always better than no location
            return true
        }

        // Check whether the new location fix is newer or older
        val timeDelta = location.time - currentBestLocation.time
        val isSignificantlyNewer = timeDelta > TWO_MINUTES
        val isSignificantlyOlder = timeDelta < -TWO_MINUTES
        val isNewer = timeDelta > 0

        // If it's been more than two minutes since the current location, use the new location
        // because the user has probably moved.
        if (isSignificantlyNewer) {
            return true
        // If the new location is more than two minutes older, it must be worse
        } else if (isSignificantlyOlder) {
            return false
        }

        // Check whether the new location fix is more or less accurate
        val accuracyDelta = location.accuracy - currentBestLocation.accuracy
        val isLessAccurate = accuracyDelta > 0
        val isMoreAccurate = accuracyDelta < 0
        val isSameAccuracy = accuracyDelta == 0

        // Check whether the new location is from the same provider
        val isSameProvider = isSameProvider(location.provider, currentBestLocation.provider)

        // Determine location quality using a combination of timeliness and accuracy
        if (isMoreAccurate) {
            return true
        } else if (isNewer && !isLessAccurate) {
            return true
        } else if (isNewer && isSameAccuracy && isSameProvider) {
            return true
        }
        return false
    }

    // Helper method to compare two providers
    private fun isSameProvider(provider1: String?, provider2: String?): Boolean {
        return if (provider1 == null) {
            provider2 == null
        } else provider1 == provider2
    }

    fun setLoadingListener(listener: LoadingListener) {
        mLoadingListener = listener
    }

    fun getLongitude(): Double = longitude
    fun getLatitude(): Double = latitude
    fun getAltitude(): Double = altitude

    // Added method to stop location updates when no longer needed
    fun stopLocationUpdates() {
        mLocationMgr.removeUpdates(mLocationListener)
        Log.d(TAG, "Location updates stopped.")
    }
}